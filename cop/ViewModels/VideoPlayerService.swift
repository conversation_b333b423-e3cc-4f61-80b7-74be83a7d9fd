//
//  VideoPlayerService.swift
//  cop
//
//  Created by AI Assistant on 2025/6/1.
//  优化的视频播放服务 - 实例化模式，轻量级配置
//

import SwiftUI
import AVFoundation
import AVKit
import Network

// MARK: - 视频播放错误类型
enum VideoPlayerError: LocalizedError {
    case fileNotFound
    case unsupportedFormat
    case networkUnavailable
    case loadingFailed
    case playerInitializationFailed

    var errorDescription: String? {
        switch self {
        case .fileNotFound:
            return "视频文件未找到"
        case .unsupportedFormat:
            return "不支持的视频格式"
        case .networkUnavailable:
            return "网络连接不可用"
        case .loadingFailed:
            return "视频加载失败"
        case .playerInitializationFailed:
            return "播放器初始化失败"
        }
    }
}

// MARK: - 播放器状态
enum VideoPlayerState: Equatable {
    case idle
    case loading
    case ready
    case playing
    case paused
    case buffering
    case error(VideoPlayerError)

    var isPlaying: Bool {
        if case .playing = self { return true }
        return false
    }
}

// MARK: - 播放器模式（简化配置）
enum VideoPlayerMode {
    case preview    // 预览模式 - 用于列表展示
    case fullScreen // 全屏模式 - 用于专注播放
    case background // 后台模式 - 最小化资源使用

    var configuration: VideoPlayerConfiguration {
        switch self {
        case .preview:
            return VideoPlayerConfiguration(
                autoPlay: false,
                showsPlaybackControls: false,
                allowsPictureInPicture: false,
                preferredForwardBufferDuration: 5.0
            )
        case .fullScreen:
            return VideoPlayerConfiguration(
                autoPlay: true,
                showsPlaybackControls: true,
                allowsPictureInPicture: true,
                preferredForwardBufferDuration: 15.0
            )
        case .background:
            return VideoPlayerConfiguration(
                autoPlay: false,
                showsPlaybackControls: false,
                allowsPictureInPicture: false,
                preferredForwardBufferDuration: 3.0
            )
        }
    }
}

// MARK: - 简化的播放器配置
struct VideoPlayerConfiguration {
    let autoPlay: Bool
    let showsPlaybackControls: Bool
    let allowsPictureInPicture: Bool
    let preferredForwardBufferDuration: TimeInterval

    // 固定的合理默认值
    let allowsExternalPlayback: Bool = true
    let videoGravity: AVLayerVideoGravity = .resizeAspect
    let allowsVideoFrameAnalysis: Bool = true

    init(
        autoPlay: Bool = true,
        showsPlaybackControls: Bool = true,
        allowsPictureInPicture: Bool = true,
        preferredForwardBufferDuration: TimeInterval = 10.0
    ) {
        self.autoPlay = autoPlay
        self.showsPlaybackControls = showsPlaybackControls
        self.allowsPictureInPicture = allowsPictureInPicture
        self.preferredForwardBufferDuration = preferredForwardBufferDuration
    }
}

// MARK: - 优化的视频播放器（实例化模式）
@MainActor
class VideoPlayer: ObservableObject {

    // MARK: - 唯一标识
    let id = UUID()

    // MARK: - Published Properties
    @Published var state: VideoPlayerState = .idle
    @Published var currentTime: TimeInterval = 0
    @Published var duration: TimeInterval = 0
    @Published var isBuffering: Bool = false
    @Published var loadedTimeRanges: [NSValue] = []

    // MARK: - Private Properties
    private var player: AVPlayer?
    private var playerItem: AVPlayerItem?
    private var timeObserver: Any?
    private var playerObservers: [NSKeyValueObservation] = []
    private var notificationObservers: [NSObjectProtocol] = []

    // 网络监控（仅在需要时启用）
    private var networkMonitor: NWPathMonitor?
    private let networkQueue = DispatchQueue(label: "VideoPlayer-\(UUID().uuidString)")

    // 配置
    private var configuration: VideoPlayerConfiguration
    private let mode: VideoPlayerMode

    // MARK: - 初始化
    init(mode: VideoPlayerMode = .fullScreen) {
        self.mode = mode
        self.configuration = mode.configuration

        // 只有网络视频才启用网络监控
        if mode != .preview {
            setupNetworkMonitoring()
        }
    }

    deinit {
        Task { @MainActor in
            cleanup()
            stopNetworkMonitoring()
        }
        print("🎬 VideoPlayer \(id) 已释放")
    }
    
    // MARK: - 公开方法

    /// 加载视频（主要方法）
    func loadVideo(from mediaFile: MediaFileInfo) {
        Task {
            await performVideoLoad(from: mediaFile)
        }
    }

    /// 从URL加载视频
    func loadVideo(from url: URL) {
        Task {
            await performVideoLoad(from: url)
        }
    }

    /// 播放视频
    func play() {
        guard let player = player else { return }
        player.play()
        if state == .ready || state == .paused {
            state = .playing
        }
    }

    /// 暂停视频
    func pause() {
        guard let player = player else { return }
        player.pause()
        if state == .playing {
            state = .paused
        }
    }

    /// 跳转到指定时间
    func seek(to time: TimeInterval) {
        guard let player = player else { return }
        let cmTime = CMTime(seconds: time, preferredTimescale: 1000)
        player.seek(to: cmTime) { [weak self] completed in
            if !completed {
                print("🎬 视频跳转失败: \(time)秒")
            }
        }
    }

    /// 设置播放速率
    func setPlaybackRate(_ rate: Float) {
        player?.rate = rate
    }

    /// 获取播放器实例（用于SwiftUI集成）
    func getPlayer() -> AVPlayer? {
        return player
    }

    /// 清理所有资源
    func cleanup() {
        print("🎬 VideoPlayer \(id) 开始清理资源")

        removeObservers()

        // 停止播放并清理播放器
        player?.pause()
        player?.rate = 0
        player?.replaceCurrentItem(with: nil)
        player = nil
        playerItem = nil

        // 重置状态
        state = .idle
        currentTime = 0
        duration = 0
        isBuffering = false
        loadedTimeRanges = []

        // 清理音频会话（仅在全屏模式下）
        if mode == .fullScreen {
            do {
                try AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
            } catch {
                print("🎬 音频会话清理失败: \(error)")
            }
        }
    }
    
    // MARK: - 私有方法

    /// 执行视频加载（MediaFileInfo）
    private func performVideoLoad(from mediaFile: MediaFileInfo) async {
        state = .loading
        cleanup()

        do {
            // 验证文件存在性
            guard FileManager.default.fileExists(atPath: mediaFile.localURL.path) else {
                throw VideoPlayerError.fileNotFound
            }

            let player = try await createPlayer(from: mediaFile.localURL, isLocal: true)
            await setupPlayer(player)

        } catch {
            state = .error(error as? VideoPlayerError ?? .loadingFailed)
            print("🎬 视频加载失败: \(error)")
        }
    }

    /// 执行视频加载（URL）
    private func performVideoLoad(from url: URL) async {
        state = .loading
        cleanup()

        do {
            let isLocal = url.isFileURL

            // 网络视频需要检查网络状态
            if !isLocal && networkMonitor == nil {
                setupNetworkMonitoring()
            }

            let player = try await createPlayer(from: url, isLocal: isLocal)
            await setupPlayer(player)

        } catch {
            state = .error(error as? VideoPlayerError ?? .loadingFailed)
            print("🎬 视频加载失败: \(error)")
        }
    }
    
    /// 创建播放器
    private func createPlayer(from url: URL, isLocal: Bool) async throws -> AVPlayer {
        let asset = AVURLAsset(url: url)
        
        // 异步加载资产属性
        let (isPlayable, duration, tracks) = try await asset.load(.isPlayable, .duration, .tracks)
        
        // 验证资产可播放性
        guard isPlayable else {
            throw VideoPlayerError.unsupportedFormat
        }
        
        // 检查视频轨道
        let videoTracks = tracks.filter { $0.mediaType == .video }
        guard !videoTracks.isEmpty else {
            throw VideoPlayerError.unsupportedFormat
        }
        
        // 更新时长信息
        self.duration = duration.seconds
        
        // 创建播放器项目
        let playerItem = AVPlayerItem(asset: asset)
        playerItem.preferredForwardBufferDuration = configuration.preferredForwardBufferDuration
        
        // 创建播放器
        let player = AVPlayer(playerItem: playerItem)
        
        return player
    }
    
    /// 设置播放器
    private func setupPlayer(_ player: AVPlayer) async {
        self.player = player
        self.playerItem = player.currentItem
        
        // 配置播放器
        configurePlayer(player)
        
        // 设置观察者
        setupObservers()
        
        // 更新状态
        state = .ready
        
        // 自动播放
        if configuration.autoPlay {
            self.play()
        }
    }
    
    /// 配置播放器
    private func configurePlayer(_ player: AVPlayer) {
        player.allowsExternalPlayback = configuration.allowsExternalPlayback
        player.automaticallyWaitsToMinimizeStalling = true

        // 只有全屏模式才配置音频会话
        if mode == .fullScreen {
            do {
                try AVAudioSession.sharedInstance().setCategory(
                    .playback,
                    mode: .moviePlayback,
                    options: [.allowAirPlay, .allowBluetooth]
                )
                try AVAudioSession.sharedInstance().setActive(true)
            } catch {
                print("🎬 音频会话设置失败: \(error)")
            }
        }
    }
    
    /// 设置观察者
    private func setupObservers() {
        guard let player = player, let playerItem = playerItem else { return }
        
        // 时间观察者
        let interval = CMTime(seconds: 0.5, preferredTimescale: 1000)
        timeObserver = player.addPeriodicTimeObserver(forInterval: interval, queue: .main) { [weak self] time in
            Task { @MainActor in
                self?.currentTime = time.seconds
            }
        }
        
        // 播放器项目观察者
        let loadedTimeRangesObserver = playerItem.observe(\.loadedTimeRanges, options: [.new]) { [weak self] item, _ in
            Task { @MainActor in
                self?.loadedTimeRanges = item.loadedTimeRanges
            }
        }
        playerObservers.append(loadedTimeRangesObserver)
        
        let bufferingObserver = playerItem.observe(\.isPlaybackLikelyToKeepUp, options: [.new]) { [weak self] item, _ in
            Task { @MainActor in
                self?.isBuffering = !item.isPlaybackLikelyToKeepUp
                if let player = self?.player, !item.isPlaybackLikelyToKeepUp && player.rate > 0 {
                    self?.state = .buffering
                } else if self?.state == .buffering && item.isPlaybackLikelyToKeepUp {
                    self?.state = .playing
                }
            }
        }
        playerObservers.append(bufferingObserver)
        
        // 播放结束通知
        let endObserver = NotificationCenter.default.addObserver(
            forName: .AVPlayerItemDidPlayToEndTime,
            object: playerItem,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.state = .ready
                self?.seek(to: 0)
            }
        }
        notificationObservers.append(endObserver)
        
        // 播放失败通知
        let failureObserver = NotificationCenter.default.addObserver(
            forName: .AVPlayerItemFailedToPlayToEndTime,
            object: playerItem,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.state = .error(.loadingFailed)
            }
        }
        notificationObservers.append(failureObserver)
    }
    
    /// 移除观察者
    private func removeObservers() {
        if let timeObserver = timeObserver {
            player?.removeTimeObserver(timeObserver)
            self.timeObserver = nil
        }
        
        playerObservers.forEach { $0.invalidate() }
        playerObservers.removeAll()
        
        notificationObservers.forEach { NotificationCenter.default.removeObserver($0) }
        notificationObservers.removeAll()
    }
    
    /// 网络监控（仅在需要时启用）
    private func setupNetworkMonitoring() {
        guard networkMonitor == nil else { return }

        let monitor = NWPathMonitor()
        networkMonitor = monitor

        monitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                if path.status == .unsatisfied {
                    self?.handleNetworkLoss()
                }
            }
        }
        monitor.start(queue: networkQueue)
        print("🎬 VideoPlayer \(id) 网络监控已启动")
    }

    private func stopNetworkMonitoring() {
        networkMonitor?.cancel()
        networkMonitor = nil
        print("🎬 VideoPlayer \(id) 网络监控已停止")
    }

    private func handleNetworkLoss() {
        if state.isPlaying {
            pause()
            state = .error(.networkUnavailable)
            print("🎬 VideoPlayer \(id) 网络连接丢失")
        }
    }
}

// MARK: - 向后兼容性别名
typealias VideoPlayerService = VideoPlayer