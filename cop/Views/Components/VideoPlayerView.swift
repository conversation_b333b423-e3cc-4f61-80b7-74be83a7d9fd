//
//  VideoPlayerView.swift
//  cop
//
//  Created by AI Assistant on 2025/6/1.
//  优化的视频播放器视图 - 实例化模式，轻量级设计
//

import SwiftUI
import AVFoundation
import AVKit

// MARK: - 优化的视频播放器视图
struct VideoPlayerView: View {
    let mediaFile: MediaFileInfo
    let mode: VideoPlayerMode

    // 回调
    let onDismiss: (() -> Void)?
    let onError: ((VideoPlayerError) -> Void)?

    // 状态管理 - 使用实例化的播放器
    @StateObject private var player: VideoPlayer
    @State private var showControls = true
    @State private var controlsTimer: Timer?

    // 手势状态
    @State private var dragOffset: CGSize = .zero
    @State private var isDragging = false
    
    init(
        mediaFile: MediaFileInfo,
        mode: VideoPlayerMode = .fullScreen,
        onDismiss: (() -> Void)? = nil,
        onError: ((VideoPlayerError) -> Void)? = nil
    ) {
        self.mediaFile = mediaFile
        self.mode = mode
        self.onDismiss = onDismiss
        self.onError = onError

        // 为每个视图创建独立的播放器实例
        self._player = StateObject(wrappedValue: VideoPlayer(mode: mode))
    }
    
    var body: some View {
        ZStack {
            // 背景
            Color.black
                .ignoresSafeArea(.all)
            
            // 主要内容
            mainContent
            
            // 控制层
            if mode == .fullScreen {
                controlsOverlay
            }
            
            // 手势处理层
            gestureOverlay
        }
        .onAppear {
            loadVideo()
        }
        .onDisappear {
            // 清理播放器资源
            player.cleanup()
        }
        .onChange(of: player.state) { _, newState in
            handleStateChange(newState)
        }
    }
    
    // MARK: - 主要内容
    @ViewBuilder
    private var mainContent: some View {
        switch player.state {
        case .idle:
            EmptyView()

        case .loading:
            loadingView

        case .ready, .playing, .paused, .buffering:
            if let avPlayer = player.getPlayer() {
                playerView(avPlayer)
            }

        case .error(let error):
            errorView(error)
        }
    }
    
    // MARK: - 加载视图
    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
            
            if mode == .fullScreen {
                Text("正在加载视频...")
                    .foregroundColor(.white)
                    .font(.headline)
            }
        }
    }
    
    // MARK: - 播放器视图
    private func playerView(_ avPlayer: AVPlayer) -> some View {
        Group {
            if mode == .fullScreen {
                // 全屏模式使用自定义控制
                ZStack {
                    AVKit.VideoPlayer(player: avPlayer)
                        .ignoresSafeArea(.all)
                        .onDisappear {
                            // 确保在视图消失时停止播放
                            avPlayer.pause()
                        }
                }
            } else {
                // 预览模式使用简单的VideoPlayer
                AVKit.VideoPlayer(player: avPlayer)
                    .onAppear {
                        // 预览模式不自动播放
                        avPlayer.pause()
                    }
            }
        }
    }
    
    // MARK: - 错误视图
    private func errorView(_ error: VideoPlayerError) -> some View {
        VStack(spacing: 24) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 64))
                .foregroundColor(.orange)
            
            Text("播放出现问题")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            Text(error.localizedDescription)
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            if mode == .fullScreen {
                HStack(spacing: 20) {
                    Button("重试") {
                        loadVideo()
                    }
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(Color.blue)
                    .cornerRadius(10)
                    
                    Button("返回") {
                        onDismiss?()
                    }
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(Color.gray)
                    .cornerRadius(10)
                }
                .padding(.top)
            }
        }
        .padding()
    }
    
    // MARK: - 控制层（仅全屏模式）
    @ViewBuilder
    private var controlsOverlay: some View {
        if showControls {
            VStack {
                // 顶部控制条
                HStack {
                    Button(action: { onDismiss?() }) {
                        Image(systemName: "chevron.left")
                            .font(.title2)
                            .foregroundColor(.white)
                            .padding()
                    }
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text(mediaFile.name)
                            .font(.headline)
                            .foregroundColor(.white)
                            .lineLimit(1)
                        
                        if player.duration > 0 {
                            Text(formatDuration(player.duration))
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    Spacer()
                }
                .padding()
                .background(
                    LinearGradient(
                        colors: [.black.opacity(0.7), .clear],
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )
                
                Spacer()
                
                // 底部控制条
                if player.state == .ready || player.state == .playing || player.state == .paused {
                    bottomControls
                }
            }
            .transition(.opacity.animation(.easeInOut(duration: 0.3)))
        }
    }
    
    // MARK: - 底部控制条
    private var bottomControls: some View {
        VStack(spacing: 16) {
            // 进度条
            progressBar
            
            // 播放控制按钮
            playbackControls
        }
        .padding()
        .background(
            LinearGradient(
                colors: [.clear, .black.opacity(0.7)],
                startPoint: .top,
                endPoint: .bottom
            )
        )
    }
    
    // MARK: - 进度条
    private var progressBar: some View {
        HStack(spacing: 12) {
            Text(formatDuration(player.currentTime))
                .font(.caption)
                .foregroundColor(.white)
                .monospacedDigit()

            ProgressSlider(
                currentTime: player.currentTime,
                duration: player.duration,
                onSeek: { time in
                    player.seek(to: time)
                }
            )

            Text(formatDuration(player.duration))
                .font(.caption)
                .foregroundColor(.white)
                .monospacedDigit()
        }
    }
    
    // MARK: - 播放控制按钮
    private var playbackControls: some View {
        HStack(spacing: 40) {
            Button(action: { seekBackward() }) {
                Image(systemName: "gobackward.10")
                    .font(.title2)
                    .foregroundColor(.white)
            }
            
            Button(action: togglePlayPause) {
                Image(systemName: player.state.isPlaying ? "pause.fill" : "play.fill")
                    .font(.title)
                    .foregroundColor(.white)
            }
            
            Button(action: { seekForward() }) {
                Image(systemName: "goforward.10")
                    .font(.title2)
                    .foregroundColor(.white)
            }
        }
    }
    
    // MARK: - 手势处理层
    private var gestureOverlay: some View {
        Color.clear
            .contentShape(Rectangle())
            .onTapGesture {
                if mode == .fullScreen {
                    toggleControls()
                } else if mode == .preview {
                    // 预览模式点击播放/暂停
                    togglePlayPause()
                }
            }
            .gesture(
                DragGesture()
                    .onChanged { value in
                        if mode == .fullScreen {
                            dragOffset = value.translation
                            isDragging = true
                        }
                    }
                    .onEnded { value in
                        if mode == .fullScreen {
                            handleDragGesture(value)
                        }
                        dragOffset = .zero
                        isDragging = false
                    }
            )
    }
    
    // MARK: - 方法
    
    private func loadVideo() {
        player.loadVideo(from: mediaFile)
    }

    private func handleStateChange(_ newState: VideoPlayerState) {
        switch newState {
        case .error(let error):
            onError?(error)
        case .ready:
            if mode == .fullScreen {
                resetControlsTimer()
            }
        default:
            break
        }
    }

    private func togglePlayPause() {
        if player.state.isPlaying {
            player.pause()
        } else {
            player.play()
        }

        if mode == .fullScreen {
            resetControlsTimer()
        }
    }

    private func seekForward() {
        let newTime = min(player.currentTime + 10, player.duration)
        player.seek(to: newTime)
        resetControlsTimer()
    }

    private func seekBackward() {
        let newTime = max(player.currentTime - 10, 0)
        player.seek(to: newTime)
        resetControlsTimer()
    }
    
    private func toggleControls() {
        withAnimation(.easeInOut(duration: 0.3)) {
            showControls.toggle()
        }
        
        if showControls {
            resetControlsTimer()
        } else {
            controlsTimer?.invalidate()
        }
    }
    
    private func resetControlsTimer() {
        controlsTimer?.invalidate()
        controlsTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: false) { _ in
            withAnimation(.easeInOut(duration: 0.3)) {
                showControls = false
            }
        }
    }
    
    private func handleDragGesture(_ value: DragGesture.Value) {
        let translation = value.translation
        
        // 向下滑动退出
        if translation.height > 100 && abs(translation.height) > abs(translation.width) * 1.5 {
            onDismiss?()
        }
    }
    
    private func formatDuration(_ timeInterval: TimeInterval) -> String {
        let totalSeconds = Int(timeInterval)
        let hours = totalSeconds / 3600
        let minutes = (totalSeconds % 3600) / 60
        let seconds = totalSeconds % 60
        
        if hours > 0 {
            return String(format: "%d:%02d:%02d", hours, minutes, seconds)
        } else {
            return String(format: "%d:%02d", minutes, seconds)
        }
    }
}

// MARK: - 进度滑块组件
struct ProgressSlider: View {
    let currentTime: TimeInterval
    let duration: TimeInterval
    let onSeek: (TimeInterval) -> Void
    
    @State private var isDragging = false
    @State private var tempValue: Double = 0
    
    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .leading) {
                // 背景轨道
                RoundedRectangle(cornerRadius: 2)
                    .fill(Color.white.opacity(0.3))
                    .frame(height: 4)
                
                // 进度轨道
                RoundedRectangle(cornerRadius: 2)
                    .fill(Color.white)
                    .frame(width: progressWidth(in: geometry), height: 4)
                
                // 拖拽手柄
                Circle()
                    .fill(Color.white)
                    .frame(width: isDragging ? 16 : 12, height: isDragging ? 16 : 12)
                    .offset(x: progressWidth(in: geometry) - (isDragging ? 8 : 6))
                    .animation(.easeInOut(duration: 0.1), value: isDragging)
            }
        }
        .frame(height: 20)
        .gesture(
            DragGesture()
                .onChanged { value in
                    isDragging = true
                    let percentage = value.location.x / UIScreen.main.bounds.width
                    tempValue = min(max(percentage * duration, 0), duration)
                }
                .onEnded { _ in
                    isDragging = false
                    onSeek(tempValue)
                }
        )
    }
    
    private func progressWidth(in geometry: GeometryProxy) -> CGFloat {
        guard duration > 0 else { return 0 }
        
        let time = isDragging ? tempValue : currentTime
        let percentage = time / duration
        return geometry.size.width * CGFloat(percentage)
    }
}

// MARK: - AVPlayerViewController 包装器
struct AVPlayerViewControllerRepresentable: UIViewControllerRepresentable {
    let player: AVPlayer
    let showsPlaybackControls: Bool
    let allowsPictureInPicture: Bool
    let allowsVideoFrameAnalysis: Bool
    
    func makeUIViewController(context: Context) -> AVPlayerViewController {
        let controller = AVPlayerViewController()
        controller.player = player
        controller.showsPlaybackControls = showsPlaybackControls
        controller.allowsPictureInPicturePlayback = allowsPictureInPicture
        controller.allowsVideoFrameAnalysis = allowsVideoFrameAnalysis
        controller.videoGravity = .resizeAspect
        return controller
    }
    
    func updateUIViewController(_ uiViewController: AVPlayerViewController, context: Context) {
        if uiViewController.player !== player {
            uiViewController.player = player
        }
    }
}

// MARK: - 预览
#if DEBUG
struct VideoPlayerView_Previews: PreviewProvider {
    static var previews: some View {
        VideoPlayerView(
            mediaFile: MediaFileInfo(
                id: UUID(),
                name: "示例视频.mp4",
                type: .video,
                fileSize: 1024000,
                creationDate: Date(),
                modificationDate: Date(),
                localURL: URL(fileURLWithPath: "/path/to/video.mp4"),
                thumbnailURL: nil,
                folderPath: "",
                duration: 120.0,
                dimensions: CGSize(width: 1920, height: 1080)
            ),
            mode: .fullScreen
        )
        .preferredColorScheme(.dark)
    }
}
#endif 