import SwiftUI
import AVFoundation

// MARK: - 现代化文件夹视图
struct ModernFolderView: View {
    @Binding var selectedSidebarItem: SidebarItem?
    @ObservedObject var folderViewModel: FolderViewModel
    
    var body: some View {
        VStack(spacing: 0) {
            if folderViewModel.selectedFolder != nil {
                // 文件夹内容视图
                FolderContentView(viewModel: folderViewModel, selectedSidebarItem: $selectedSidebarItem)
            } else {
                // 文件夹列表视图
                FolderListView(viewModel: folderViewModel, selectedSidebarItem: $selectedSidebarItem)
            }
            
            // 底部提示信息
            if let errorMessage = folderViewModel.errorMessage {
                AppCard {
                    HStack {
                        Image(systemName: "info.circle")
                            .foregroundColor(AppDesignSystem.Colors.warning)
                        Text(errorMessage)
                            .font(AppDesignSystem.Typography.caption1)
                            .foregroundColor(AppDesignSystem.Colors.textSecondary)
                        Spacer()
                        AppButton("关闭", style: .tertiary, size: .small) {
                            folderViewModel.clearError()
                        }
                    }
                }
                .padding(.horizontal, AppDesignSystem.Spacing.lg)
                .transition(.move(edge: .bottom))
            }
        }
    }
}

// MARK: - 文件夹列表视图
struct FolderListView: View {
    @ObservedObject var viewModel: FolderViewModel
    @Binding var selectedSidebarItem: SidebarItem?
    
    var body: some View {
        VStack(spacing: 0) {
            // 工具栏
            FolderListToolbar(viewModel: viewModel, selectedSidebarItem: $selectedSidebarItem)
            
            // 主内容
            if viewModel.isLoading {
                AppLoadingView("正在加载文件夹...", style: .skeleton)
            } else if viewModel.folders.isEmpty {
                AppEmptyStateView(
                    icon: "folder.badge.plus",
                    title: "暂无文件夹",
                    description: "导入媒体文件夹以开始管理您的文件",
                    actionTitle: "导入文件夹"
                ) {
                    // 跳转到导入管理页面
                    selectedSidebarItem = .importManager
                }
            } else {
                // 根据视图模式显示不同的内容
                switch viewModel.folderBrowseMode {
                case .grid:
                    FolderGridView(viewModel: viewModel)
                case .list:
                    FolderListBrowserView(viewModel: viewModel)
                }
            }
        }
    }
}

// MARK: - 文件夹列表工具栏
struct FolderListToolbar: View {
    @ObservedObject var viewModel: FolderViewModel
    @Binding var selectedSidebarItem: SidebarItem?
    
    var body: some View {
        VStack(spacing: AppDesignSystem.Spacing.md) {
            HStack(spacing: AppDesignSystem.Spacing.md) {
                AppSearchField("搜索文件夹...", text: $viewModel.folderSearchText)
                    .frame(maxWidth: .infinity)
                
                // 导入按钮 - 点击跳转到导入管理页面
                AppButton("导入", icon: "plus.circle.fill", size: .small) {
                    selectedSidebarItem = .importManager
                }
                
                // 排序菜单
                FolderSortMenu(viewModel: viewModel)
                
                // 视图模式菜单
                FolderViewModeMenu(viewModel: viewModel)
            }
        }
        .padding(.horizontal, AppDesignSystem.Spacing.xl)
        .padding(.vertical, AppDesignSystem.Spacing.lg)
        .frame(minHeight: AppDesignSystem.Spacing.toolbarHeight)
        .background(AppDesignSystem.Colors.material)
    }
}

// MARK: - 文件夹排序菜单
struct FolderSortMenu: View {
    @ObservedObject var viewModel: FolderViewModel
    
    var body: some View {
        Menu {
            Section("排序方式") {
                ForEach([FolderSortOption.name, .size, .fileCount, .dateModified], id: \.self) { option in
                    Button(action: { 
                        if viewModel.folderSortOption == option {
                            // 如果是同一个选项，切换排序方向
                            viewModel.folderSortDirection = viewModel.folderSortDirection == .ascending ? .descending : .ascending
                        } else {
                            viewModel.setSortOption(option)
                        }
                    }) {
                        HStack {
                            Text(option.displayName)
                            Spacer()
                            
                            if viewModel.folderSortOption == option {
                                HStack(spacing: 4) {
                                    Image(systemName: viewModel.folderSortDirection == .ascending ? "arrow.up" : "arrow.down")
                                        .foregroundColor(AppDesignSystem.Colors.primary)
                                    Image(systemName: "checkmark")
                                        .foregroundColor(AppDesignSystem.Colors.primary)
                                }
                            }
                        }
                    }
                }
            }
        } label: {
            HStack(spacing: 6) {
                Image(systemName: sortIcon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(AppDesignSystem.Colors.primary)
                
                Text("\(sortLabel) \(sortDirection)")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppDesignSystem.Colors.primary)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(AppDesignSystem.Colors.primary.opacity(0.1))
            )
        }
    }
    
    private var sortIcon: String {
        viewModel.folderSortDirection == .ascending ? "arrow.up.circle.fill" : "arrow.down.circle.fill"
    }
    
    private var sortLabel: String {
        switch viewModel.folderSortOption {
        case .name: return "名称"
        case .size: return "大小"
        case .fileCount: return "文件数"
        case .dateModified: return "修改"
        default: return "排序"
        }
    }
    
    private var sortDirection: String {
        viewModel.folderSortDirection == .ascending ? "↑" : "↓"
    }
}

// MARK: - 文件夹视图模式菜单
struct FolderViewModeMenu: View {
    @ObservedObject var viewModel: FolderViewModel
    
    var body: some View {
        Menu {
            Section("视图模式") {
                Button(action: { viewModel.setFolderViewMode(.grid) }) {
                    HStack {
                        Text("网格视图")
                        Spacer()
                        if viewModel.folderBrowseMode == .grid {
                            Image(systemName: "checkmark")
                                .foregroundColor(AppDesignSystem.Colors.primary)
                        }
                    }
                }
                
                Button(action: { viewModel.setFolderViewMode(.list) }) {
                    HStack {
                        Text("列表视图")
                        Spacer()
                        if viewModel.folderBrowseMode == .list {
                            Image(systemName: "checkmark")
                                .foregroundColor(AppDesignSystem.Colors.primary)
                        }
                    }
                }
            }
        } label: {
            HStack(spacing: 6) {
                Image(systemName: viewModeIcon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(AppDesignSystem.Colors.icon)
                
                Text(viewModeLabel)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppDesignSystem.Colors.icon)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.clear)
            )
        }
    }
    
    private var viewModeIcon: String {
        switch viewModel.folderBrowseMode {
        case .grid: return "square.grid.2x2"
        case .list: return "list.bullet"
        }
    }
    
    private var viewModeLabel: String {
        switch viewModel.folderBrowseMode {
        case .grid: return "网格"
        case .list: return "列表"
        }
    }
}

// MARK: - 文件夹网格视图（优化版）
struct FolderGridView: View {
    @ObservedObject var viewModel: FolderViewModel
    
    var body: some View {
        GeometryReader { geometry in
            ScrollView {
                LazyVGrid(
                    columns: adaptiveColumns(for: geometry.size.width),
                    spacing: AppDesignSystem.Layout.gridSpacing
                ) {
                    ForEach(viewModel.displayedFolders, id: \.name) { folder in
                        FolderCard(folder: folder) {
                            viewModel.selectFolder(folder)
                        }
                        .contextMenu {
                            FolderContextMenu(folder: folder, viewModel: viewModel)
                        }
                    }
                }
                .padding(.horizontal, AppDesignSystem.Spacing.lg)
                .padding(.vertical, AppDesignSystem.Spacing.md)
            }
        }
    }
    
    private func adaptiveColumns(for width: CGFloat) -> [GridItem] {
        let availableWidth = width - (AppDesignSystem.Spacing.lg * 2)
        let minCardWidth: CGFloat = 200
        let spacing = AppDesignSystem.Layout.gridSpacing
        
        // 计算可容纳的列数
        let columnsCount = max(2, Int((availableWidth + spacing) / (minCardWidth + spacing)))
        
        // 创建等宽的列
        return Array(repeating: GridItem(.flexible(), spacing: spacing), count: columnsCount)
    }
}

// MARK: - 文件夹卡片（优化版，增加缩略图展示）
struct FolderCard: View {
    let folder: FolderInfo
    let onTap: () -> Void
    
    @State private var isHovered = false
    @State private var folderThumbnail: UIImage?
    @State private var isLoadingThumbnail = false
    
    var body: some View {
        VStack(spacing: AppDesignSystem.Spacing.md) {
            // 文件夹图标和缩略图
            ZStack {
                RoundedRectangle(cornerRadius: AppDesignSystem.CornerRadius.lg)
                    .fill(AppDesignSystem.Colors.backgroundSecondary)
                    .frame(height: 120)
                
                if let thumbnail = folderThumbnail {
                    // 显示文件夹缩略图
                    Image(uiImage: thumbnail)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(height: 120)
                        .clipped()
                        .cornerRadius(AppDesignSystem.CornerRadius.lg)
                } else if isLoadingThumbnail {
                    // 加载中状态
                    ProgressView()
                        .scaleEffect(1.2)
                        .foregroundColor(AppDesignSystem.Colors.primary)
                } else if let thumbnailURL = folder.thumbnail {
                    // 使用已有的缩略图URL
                    AsyncImage(url: thumbnailURL) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(height: 120)
                            .clipped()
                            .cornerRadius(AppDesignSystem.CornerRadius.lg)
                    } placeholder: {
                        Image(systemName: "folder.fill")
                            .font(.system(size: 40))
                            .foregroundColor(AppDesignSystem.Colors.primary)
                    }
                } else {
                    // 默认文件夹图标
                    Image(systemName: "folder.fill")
                        .font(.system(size: 40))
                        .foregroundColor(AppDesignSystem.Colors.primary)
                }
                
                // 文件数量角标
                VStack {
                    HStack {
                        Spacer()
                        Text("\(folder.mediaCount)")
                            .font(AppDesignSystem.Typography.caption2)
                            .fontWeight(.medium)
                            .foregroundColor(.white)
                            .padding(.horizontal, AppDesignSystem.Spacing.sm)
                            .padding(.vertical, AppDesignSystem.Spacing.xs)
                            .background(.black.opacity(0.6))
                            .cornerRadius(AppDesignSystem.CornerRadius.sm)
                    }
                    Spacer()
                }
                .padding(AppDesignSystem.Spacing.sm)
            }
            
            // 文件夹信息
            VStack(alignment: .leading, spacing: AppDesignSystem.Spacing.xs) {
                // 固定1行高度的文件夹名区域
                Text(folder.name)
                    .font(AppDesignSystem.Typography.bodyEmphasized)
                    .foregroundColor(AppDesignSystem.Colors.text)
                    .lineLimit(1)
                    .frame(height: calculateFolderNameHeight(), alignment: .top)

                HStack {
                    Text(folder.formattedTotalSize)
                        .font(AppDesignSystem.Typography.caption2)
                        .foregroundColor(AppDesignSystem.Colors.textSecondary)

                    Spacer()

                    Text(DateFormatter.relative.localizedString(for: folder.lastModified, relativeTo: Date()))
                        .font(AppDesignSystem.Typography.caption2)
                        .foregroundColor(AppDesignSystem.Colors.textTertiary)
                }
            }
        }
        .onTapGesture {
            onTap()
        }
        .scaleEffect(isHovered ? 1.02 : 1.0)
        .animation(AppDesignSystem.Animation.quick, value: isHovered)
        .onHover { hovering in
            isHovered = hovering
        }
        .onAppear {
            loadFolderThumbnail()
        }
    }

    // 计算固定的1行文件夹名高度
    private func calculateFolderNameHeight() -> CGFloat {
        let font = UIFont.systemFont(ofSize: 17, weight: .medium) // bodyEmphasized 对应的字体
        return font.lineHeight
    }
    
    private func loadFolderThumbnail() {
        // 如果已有thumbnail或正在加载，直接返回
        if folder.thumbnail != nil || isLoadingThumbnail || folderThumbnail != nil {
            return
        }
        
        isLoadingThumbnail = true
        
        Task {
            do {
                let thumbnail = try await generateFolderThumbnail()
                await MainActor.run {
                    self.folderThumbnail = thumbnail
                    self.isLoadingThumbnail = false
                }
            } catch {
                await MainActor.run {
                    self.isLoadingThumbnail = false
                }
            }
        }
    }
    
    private func generateFolderThumbnail() async throws -> UIImage? {
        // 获取文件夹路径
        let folderURL = URL(fileURLWithPath: folder.path)
        
        // 获取文件夹中的媒体文件
        let fileManager = FileManager.default
        let fileURLs = try fileManager.contentsOfDirectory(
            at: folderURL,
            includingPropertiesForKeys: [.isRegularFileKey, .typeIdentifierKey],
            options: [.skipsHiddenFiles]
        ).filter { url in
            let resourceValues = try? url.resourceValues(forKeys: [.isRegularFileKey])
            return resourceValues?.isRegularFile == true && isSupportedMediaFile(url)
        }
        
        // 如果没有媒体文件，返回nil
        guard let firstMediaFile = fileURLs.first else {
            return nil
        }
        
        // 生成第一个媒体文件的缩略图
        return try await generateThumbnailForFile(firstMediaFile)
    }
    
    private func isSupportedMediaFile(_ url: URL) -> Bool {
        let fileExtension = url.pathExtension.lowercased()
        let supportedExtensions = ["jpg", "jpeg", "png", "heic", "heif", "gif", "tiff", "bmp", "mp4", "mov", "avi", "mkv", "m4v"]
        return supportedExtensions.contains(fileExtension)
    }
    
    private func generateThumbnailForFile(_ url: URL) async throws -> UIImage? {
        let fileExtension = url.pathExtension.lowercased()
        let imageExtensions = ["jpg", "jpeg", "png", "heic", "heif", "gif", "tiff", "bmp"]
        
        if imageExtensions.contains(fileExtension) {
            // 生成图片缩略图
            return try await generateImageThumbnail(from: url)
        } else {
            // 生成视频缩略图
            return try await generateVideoThumbnail(from: url)
        }
    }
    
    private func generateImageThumbnail(from url: URL) async throws -> UIImage? {
        guard let imageSource = CGImageSourceCreateWithURL(url as CFURL, nil) else {
            return nil
        }
        
        let options: [CFString: Any] = [
            kCGImageSourceCreateThumbnailFromImageIfAbsent: true,
            kCGImageSourceCreateThumbnailWithTransform: true,
            kCGImageSourceThumbnailMaxPixelSize: 300
        ]
        
        guard let thumbnail = CGImageSourceCreateThumbnailAtIndex(imageSource, 0, options as CFDictionary) else {
            return nil
        }
        
        return UIImage(cgImage: thumbnail)
    }
    
    private func generateVideoThumbnail(from url: URL) async throws -> UIImage? {
        let asset = AVURLAsset(url: url)
        let imageGenerator = AVAssetImageGenerator(asset: asset)
        imageGenerator.appliesPreferredTrackTransform = true
        imageGenerator.maximumSize = CGSize(width: 300, height: 300)
        
        let time = CMTimeMake(value: 1, timescale: 1)
        
        do {
            let cgImage = try await imageGenerator.image(at: time).image
            return UIImage(cgImage: cgImage)
        } catch {
            return nil
        }
    }
}

// MARK: - 文件夹上下文菜单
struct FolderContextMenu: View {
    let folder: FolderInfo
    let viewModel: FolderViewModel
    
    var body: some View {
        VStack {
            AppButton("打开", icon: "folder.fill") {
                viewModel.selectFolder(folder)
            }
            
            AppButton("重新扫描", icon: "arrow.clockwise") {
                Task {
                    await viewModel.rescanFolder(folder)
                }
            }
            
            AppButton("导出", icon: "square.and.arrow.up") {
                // 导出功能
            }
            
            Divider()
            
            AppButton("删除", icon: "trash", style: .destructive) {
                Task {
                    await viewModel.deleteFolder(folder)
                }
            }
        }
    }
}

// MARK: - 文件夹内容视图
struct FolderContentView: View {
    @ObservedObject var viewModel: FolderViewModel
    @Binding var selectedSidebarItem: SidebarItem?
    
    var body: some View {
        VStack(spacing: 0) {
            // 工具栏（仅保留工具栏，导航栏由MainView处理）
            FolderContentToolbar(viewModel: viewModel)
            
            // 主内容区域
            if viewModel.isLoading {
                AppLoadingView("正在加载媒体文件...", style: .skeleton)
            } else if viewModel.folderMediaFiles.isEmpty {
                AppEmptyStateView(
                    icon: "photo.badge.plus",
                    title: "暂无媒体文件",
                    description: "此文件夹中没有媒体文件",
                    actionTitle: "重新扫描"
                ) {
                    Task {
                        if let folder = viewModel.selectedFolder {
                            await viewModel.rescanFolder(folder)
                        }
                    }
                }
            } else {
                FolderContentView_Media(viewModel: viewModel)
            }
        }
        .fullScreenCover(item: $viewModel.fullScreenMediaItem) { item in
            FullScreenMediaViewer(
                mediaFiles: item.mediaFiles,
                initialIndex: item.initialIndex,
                viewModel: createSharedMediaLibraryViewModel(),
                initialUIState: item.initialUIState
            )
        }
    }

    private func createSharedMediaLibraryViewModel() -> MediaLibraryViewModel {
        // Create a MediaLibraryViewModel for fullscreen operations
        return MediaLibraryViewModel()
    }
}

// MARK: - 文件夹媒体内容视图
struct FolderContentView_Media: View {
    @ObservedObject var viewModel: FolderViewModel
    
    var body: some View {
        switch viewModel.viewMode {
        case .grid:
            FolderMediaGridBrowser(viewModel: viewModel)
        case .list:
            FolderMediaListBrowser(viewModel: viewModel)
        case .waterfall:
            FolderMediaWaterfallBrowser(viewModel: viewModel)
        }
    }
}

// MARK: - 文件夹媒体网格浏览器
struct FolderMediaGridBrowser: View {
    @ObservedObject var viewModel: FolderViewModel
    
    private let gridColumns = [
        GridItem(.adaptive(minimum: 160), spacing: AppDesignSystem.Layout.gridSpacing)
    ]
    
    var body: some View {
        GeometryReader { geometry in
            ScrollView {
                LazyVGrid(
                    columns: adaptiveColumns(for: geometry.size.width),
                    spacing: AppDesignSystem.Layout.gridSpacing
                ) {
                    ForEach(Array(viewModel.displayedMediaFiles.enumerated()), id: \.element.id) { index, mediaFile in
                        MediaCard(
                            mediaFile: mediaFile,
                            size: calculateCardSize(for: geometry.size.width)
                        ) {
                            createSharedMediaLibraryViewModel().showFullScreenViewer(
                                mediaFiles: viewModel.displayedMediaFiles,
                                at: index,
                                initialUIState: mediaFile.type == .video ? .hidden : .visible
                            )
                        } onLongPress: {
                            // 长按操作
                        }
                        .contextMenu {
                            FolderMediaContextMenu(mediaFile: mediaFile, viewModel: viewModel)
                        }
                    }
                }
                .padding(.horizontal, AppDesignSystem.Spacing.lg)
                .padding(.vertical, AppDesignSystem.Spacing.md)
            }
        }
    }

    private func adaptiveColumns(for width: CGFloat) -> [GridItem] {
        let availableWidth = width - (AppDesignSystem.Spacing.lg * 2)
        let minCardWidth: CGFloat = 160
        let spacing = AppDesignSystem.Layout.gridSpacing

        let columnsCount = max(2, Int((availableWidth + spacing) / (minCardWidth + spacing)))
        return Array(repeating: GridItem(.flexible(), spacing: spacing), count: columnsCount)
    }

    private func calculateCardSize(for width: CGFloat) -> CGSize {
        let availableWidth = width - (AppDesignSystem.Spacing.lg * 2)
        let columns = adaptiveColumns(for: width).count
        let cardWidth = (availableWidth - CGFloat(columns - 1) * AppDesignSystem.Layout.gridSpacing) / CGFloat(columns)
        return CGSize(width: cardWidth, height: cardWidth + 50)
    }

    private func createSharedMediaLibraryViewModel() -> MediaLibraryViewModel {
        // Create a MediaLibraryViewModel for fullscreen operations
        // Since the fullscreen viewer mainly needs delete functionality,
        // we create a minimal working instance
        return MediaLibraryViewModel()
    }
}

// MARK: - 文件夹媒体列表浏览器
struct FolderMediaListBrowser: View {
    @ObservedObject var viewModel: FolderViewModel
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: AppDesignSystem.Spacing.xs) {
                ForEach(Array(viewModel.displayedMediaFiles.enumerated()), id: \.element.id) { index, mediaFile in
                    MediaListRow(mediaFile: mediaFile) {
                        createSharedMediaLibraryViewModel().showFullScreenViewer(
                            mediaFiles: viewModel.displayedMediaFiles,
                            at: index,
                            initialUIState: mediaFile.type == .video ? .hidden : .visible
                        )
                    }
                    .contextMenu {
                        FolderMediaContextMenu(mediaFile: mediaFile, viewModel: viewModel)
                    }
                }
            }
            .padding(.horizontal, AppDesignSystem.Spacing.lg)
        }
    }

    private func createSharedMediaLibraryViewModel() -> MediaLibraryViewModel {
        // Create a MediaLibraryViewModel for fullscreen operations
        return MediaLibraryViewModel()
    }
}

// MARK: - 文件夹媒体瀑布流浏览器
struct FolderMediaWaterfallBrowser: View {
    @ObservedObject var viewModel: FolderViewModel
    
    var body: some View {
        ScrollView {
            ModernWaterfallBrowser(
                mediaFiles: viewModel.displayedMediaFiles,
                viewModel: createSharedMediaLibraryViewModel()
            )
        }
    }

    private func createSharedMediaLibraryViewModel() -> MediaLibraryViewModel {
        // Create a MediaLibraryViewModel for fullscreen operations
        return MediaLibraryViewModel()
    }
}

// MARK: - 文件夹媒体上下文菜单
struct FolderMediaContextMenu: View {
    let mediaFile: MediaFileInfo
    let viewModel: FolderViewModel
    
    var body: some View {
        VStack {
            Button("查看详情", systemImage: "info.circle") {
                // TODO: 实现查看详情
            }
            
            Button("分享", systemImage: "square.and.arrow.up") {
                // TODO: 实现分享功能
            }
            
            Divider()
            
            Button("删除", systemImage: "trash", role: .destructive) {
                Task {
                    await viewModel.deleteMediaFile(mediaFile)
                }
            }
        }
    }
}

// MARK: - 文件夹媒体筛选菜单
struct FolderMediaFilterMenu: View {
    @ObservedObject var viewModel: FolderViewModel
    
    var body: some View {
        Menu {
            Section("媒体类型") {
                Button(action: { viewModel.selectedMediaType = nil }) {
                    HStack {
                        Text("全部")
                        Spacer()
                        if viewModel.selectedMediaType == nil {
                            Image(systemName: "checkmark")
                                .foregroundColor(AppDesignSystem.Colors.primary)
                        }
                    }
                }
                
                Button(action: { viewModel.selectedMediaType = .image }) {
                    HStack {
                        Text("图片")
                        Spacer()
                        if viewModel.selectedMediaType == .image {
                            Image(systemName: "checkmark")
                                .foregroundColor(AppDesignSystem.Colors.primary)
                        }
                    }
                }
                
                Button(action: { viewModel.selectedMediaType = .video }) {
                    HStack {
                        Text("视频")
                        Spacer()
                        if viewModel.selectedMediaType == .video {
                            Image(systemName: "checkmark")
                                .foregroundColor(AppDesignSystem.Colors.primary)
                        }
                    }
                }
            }
        } label: {
            HStack(spacing: 6) {
                Image(systemName: filterIcon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(isFilterActive ? AppDesignSystem.Colors.primary : AppDesignSystem.Colors.icon)
                
                Text(isFilterActive ? filterLabel : "筛选")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(isFilterActive ? AppDesignSystem.Colors.primary : AppDesignSystem.Colors.icon)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isFilterActive ? AppDesignSystem.Colors.primary.opacity(0.1) : Color.clear)
            )
        }
    }
    
    private var isFilterActive: Bool {
        viewModel.selectedMediaType != nil
    }
    
    private var filterIcon: String {
        switch viewModel.selectedMediaType {
        case .image: return "photo"
        case .video: return "video"
        case .none: return "line.3.horizontal.decrease.circle"
        }
    }
    
    private var filterLabel: String {
        switch viewModel.selectedMediaType {
        case .image: return "图片"
        case .video: return "视频"
        case .none: return ""
        }
    }
}

// MARK: - 文件夹媒体排序菜单
struct FolderMediaSortMenu: View {
    @ObservedObject var viewModel: FolderViewModel
    
    var body: some View {
        Menu {
            Section("排序方式") {
                ForEach([MediaSortOption.name, .dateCreated, .dateModified, .fileSize, .type], id: \.self) { option in
                    Button(action: { 
                        if viewModel.sortOption == option {
                            // 如果是同一个选项，切换排序方向
                            viewModel.sortDirection = viewModel.sortDirection == .ascending ? .descending : .ascending
                        } else {
                            viewModel.sortOption = option
                        }
                    }) {
                        HStack {
                            Text(option.displayName)
                            Spacer()
                            
                            if viewModel.sortOption == option {
                                HStack(spacing: 4) {
                                    Image(systemName: viewModel.sortDirection == .ascending ? "arrow.up" : "arrow.down")
                                        .foregroundColor(AppDesignSystem.Colors.primary)
                                    Image(systemName: "checkmark")
                                        .foregroundColor(AppDesignSystem.Colors.primary)
                                }
                            }
                        }
                    }
                }
            }
        } label: {
            HStack(spacing: 6) {
                Image(systemName: sortIcon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(AppDesignSystem.Colors.primary)
                
                Text("\(sortLabel) \(sortDirection)")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppDesignSystem.Colors.primary)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(AppDesignSystem.Colors.primary.opacity(0.1))
            )
        }
    }
    
    private var sortIcon: String {
        viewModel.sortDirection == .ascending ? "arrow.up.circle.fill" : "arrow.down.circle.fill"
    }
    
    private var sortLabel: String {
        switch viewModel.sortOption {
        case .name: return "名称"
        case .dateCreated: return "创建"
        case .dateModified: return "修改"
        case .fileSize: return "大小"
        case .type: return "类型"
        }
    }
    
    private var sortDirection: String {
        viewModel.sortDirection == .ascending ? "↑" : "↓"
    }
}

// MARK: - 文件夹媒体视图模式菜单
struct FolderMediaViewModeMenu: View {
    @ObservedObject var viewModel: FolderViewModel
    
    var body: some View {
        Menu {
            Section("视图模式") {
                Button(action: { viewModel.setViewMode(.grid) }) {
                    HStack {
                        Text("网格视图")
                        Spacer()
                        if viewModel.viewMode == .grid {
                            Image(systemName: "checkmark")
                                .foregroundColor(AppDesignSystem.Colors.primary)
                        }
                    }
                }
                
                Button(action: { viewModel.setViewMode(.list) }) {
                    HStack {
                        Text("列表视图")
                        Spacer()
                        if viewModel.viewMode == .list {
                            Image(systemName: "checkmark")
                                .foregroundColor(AppDesignSystem.Colors.primary)
                        }
                    }
                }
                
                Button(action: { viewModel.setViewMode(.waterfall) }) {
                    HStack {
                        Text("瀑布视图")
                        Spacer()
                        if viewModel.viewMode == .waterfall {
                            Image(systemName: "checkmark")
                                .foregroundColor(AppDesignSystem.Colors.primary)
                        }
                    }
                }
            }
        } label: {
            HStack(spacing: 6) {
                Image(systemName: viewModeIcon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(AppDesignSystem.Colors.icon)
                
                Text(viewModeLabel)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppDesignSystem.Colors.icon)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.clear)
            )
        }
    }
    
    private var viewModeIcon: String {
        switch viewModel.viewMode {
        case .grid: return "square.grid.2x2"
        case .list: return "list.bullet"
        case .waterfall: return "rectangle.grid.1x2"
        }
    }
    
    private var viewModeLabel: String {
        switch viewModel.viewMode {
        case .grid: return "网格"
        case .list: return "列表"
        case .waterfall: return "瀑布"
        }
    }
}

// MARK: - 文件夹内容工具栏
struct FolderContentToolbar: View {
    @ObservedObject var viewModel: FolderViewModel
    
    var body: some View {
        VStack(spacing: AppDesignSystem.Spacing.md) {
            HStack(spacing: AppDesignSystem.Spacing.md) {
                AppSearchField("搜索媒体文件...", text: $viewModel.searchText)
                    .frame(maxWidth: .infinity)
                
                // 媒体类型筛选
                FolderMediaFilterMenu(viewModel: viewModel)
                
                // 排序菜单
                FolderMediaSortMenu(viewModel: viewModel)
                
                // 视图模式菜单
                FolderMediaViewModeMenu(viewModel: viewModel)
            }
        }
        .padding(.horizontal, AppDesignSystem.Spacing.xl)
        .padding(.vertical, AppDesignSystem.Spacing.lg)
        .frame(minHeight: AppDesignSystem.Spacing.toolbarHeight)
        .background(AppDesignSystem.Colors.material)
    }
}

// MARK: - 扩展DateFormatter
extension DateFormatter {
    static let relative: RelativeDateTimeFormatter = {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter
    }()
}

// MARK: - 文件夹列表浏览视图
struct FolderListBrowserView: View {
    @ObservedObject var viewModel: FolderViewModel
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: AppDesignSystem.Spacing.md) {
                ForEach(viewModel.displayedFolders, id: \.name) { folder in
                    FolderListCard(folder: folder) {
                        viewModel.selectFolder(folder)
                    }
                    .contextMenu {
                        FolderContextMenu(folder: folder, viewModel: viewModel)
                    }
                }
            }
            .padding(.horizontal, AppDesignSystem.Spacing.lg)
            .padding(.vertical, AppDesignSystem.Spacing.md)
        }
    }
}

// MARK: - 文件夹列表卡片
struct FolderListCard: View {
    let folder: FolderInfo
    let onTap: () -> Void
    
    @State private var folderThumbnail: UIImage?
    @State private var isLoadingThumbnail = false
    
    var body: some View {
        HStack(spacing: AppDesignSystem.Spacing.lg) {
            // 文件夹缩略图
            ZStack {
                RoundedRectangle(cornerRadius: AppDesignSystem.CornerRadius.md)
                    .fill(AppDesignSystem.Colors.backgroundSecondary)
                    .frame(width: 80, height: 60)
                
                if let thumbnail = folderThumbnail {
                    Image(uiImage: thumbnail)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 80, height: 60)
                        .clipped()
                        .cornerRadius(AppDesignSystem.CornerRadius.md)
                } else if isLoadingThumbnail {
                    ProgressView()
                        .scaleEffect(0.8)
                        .foregroundColor(AppDesignSystem.Colors.primary)
                } else if let thumbnailURL = folder.thumbnail {
                    AsyncImage(url: thumbnailURL) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 80, height: 60)
                            .clipped()
                            .cornerRadius(AppDesignSystem.CornerRadius.md)
                    } placeholder: {
                        Image(systemName: "folder.fill")
                            .font(.title)
                            .foregroundColor(AppDesignSystem.Colors.primary)
                    }
                } else {
                    Image(systemName: "folder.fill")
                        .font(.title)
                        .foregroundColor(AppDesignSystem.Colors.primary)
                }
            }
            
            // 文件夹信息
            VStack(alignment: .leading, spacing: AppDesignSystem.Spacing.sm) {
                Text(folder.name)
                    .font(AppDesignSystem.Typography.bodyEmphasized)
                    .foregroundColor(AppDesignSystem.Colors.text)
                
                HStack {
                    Label("\(folder.mediaCount) 个文件", systemImage: "photo")
                        .font(AppDesignSystem.Typography.caption1)
                        .foregroundColor(AppDesignSystem.Colors.textSecondary)
                    
                    Spacer()
                    
                    Text(folder.formattedTotalSize)
                        .font(AppDesignSystem.Typography.caption1)
                        .foregroundColor(AppDesignSystem.Colors.textSecondary)
                }
                
                Text(DateFormatter.relative.localizedString(for: folder.lastModified, relativeTo: Date()))
                    .font(AppDesignSystem.Typography.caption2)
                    .foregroundColor(AppDesignSystem.Colors.textTertiary)
            }
            
            Spacer()
            
            // 箭头指示器
            Image(systemName: "chevron.right")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(AppDesignSystem.Colors.iconSecondary)
        }
        .padding(AppDesignSystem.Spacing.lg)
        .background(
            RoundedRectangle(cornerRadius: AppDesignSystem.CornerRadius.lg)
                .fill(AppDesignSystem.Colors.backgroundSecondary)
        )
        .onTapGesture {
            onTap()
        }
        .onAppear {
            loadFolderThumbnail()
        }
    }
    
    private func loadFolderThumbnail() {
        // 如果已有thumbnail或正在加载，直接返回
        if folder.thumbnail != nil || isLoadingThumbnail || folderThumbnail != nil {
            return
        }
        
        isLoadingThumbnail = true
        
        Task {
            do {
                let thumbnail = try await generateFolderThumbnail()
                await MainActor.run {
                    self.folderThumbnail = thumbnail
                    self.isLoadingThumbnail = false
                }
            } catch {
                await MainActor.run {
                    self.isLoadingThumbnail = false
                }
            }
        }
    }
    
    private func generateFolderThumbnail() async throws -> UIImage? {
        // 获取文件夹路径
        let folderURL = URL(fileURLWithPath: folder.path)
        
        // 获取文件夹中的媒体文件
        let fileManager = FileManager.default
        let fileURLs = try fileManager.contentsOfDirectory(
            at: folderURL,
            includingPropertiesForKeys: [.isRegularFileKey, .typeIdentifierKey],
            options: [.skipsHiddenFiles]
        ).filter { url in
            let resourceValues = try? url.resourceValues(forKeys: [.isRegularFileKey])
            return resourceValues?.isRegularFile == true && isSupportedMediaFile(url)
        }
        
        // 如果没有媒体文件，返回nil
        guard let firstMediaFile = fileURLs.first else {
            return nil
        }
        
        // 生成第一个媒体文件的缩略图
        return try await generateThumbnailForFile(firstMediaFile)
    }
    
    private func isSupportedMediaFile(_ url: URL) -> Bool {
        let fileExtension = url.pathExtension.lowercased()
        let supportedExtensions = ["jpg", "jpeg", "png", "heic", "heif", "gif", "tiff", "bmp", "mp4", "mov", "avi", "mkv", "m4v"]
        return supportedExtensions.contains(fileExtension)
    }
    
    private func generateThumbnailForFile(_ url: URL) async throws -> UIImage? {
        let fileExtension = url.pathExtension.lowercased()
        let imageExtensions = ["jpg", "jpeg", "png", "heic", "heif", "gif", "tiff", "bmp"]
        
        if imageExtensions.contains(fileExtension) {
            // 生成图片缩略图
            return try await generateImageThumbnail(from: url)
        } else {
            // 生成视频缩略图
            return try await generateVideoThumbnail(from: url)
        }
    }
    
    private func generateImageThumbnail(from url: URL) async throws -> UIImage? {
        guard let imageSource = CGImageSourceCreateWithURL(url as CFURL, nil) else {
            return nil
        }
        
        let options: [CFString: Any] = [
            kCGImageSourceCreateThumbnailFromImageIfAbsent: true,
            kCGImageSourceCreateThumbnailWithTransform: true,
            kCGImageSourceThumbnailMaxPixelSize: 300
        ]
        
        guard let thumbnail = CGImageSourceCreateThumbnailAtIndex(imageSource, 0, options as CFDictionary) else {
            return nil
        }
        
        return UIImage(cgImage: thumbnail)
    }
    
    private func generateVideoThumbnail(from url: URL) async throws -> UIImage? {
        let asset = AVURLAsset(url: url)
        let imageGenerator = AVAssetImageGenerator(asset: asset)
        imageGenerator.appliesPreferredTrackTransform = true
        imageGenerator.maximumSize = CGSize(width: 300, height: 300)
        
        let time = CMTimeMake(value: 1, timescale: 1)
        
        do {
            let cgImage = try await imageGenerator.image(at: time).image
            return UIImage(cgImage: cgImage)
        } catch {
            return nil
        }
    }
}

// MARK: - 预览
#Preview {
    ModernFolderView(selectedSidebarItem: .constant(nil), folderViewModel: FolderViewModel())
} 