import Foundation
import WebKit
import SwiftUI
import OSLog

// MARK: - 简化的性能指标数据结构
struct BrowserMetrics {
    var memoryUsage: UInt64 = 0
    var activeWebViews: Int = 0
    var networkErrors: Int = 0
    
    var formattedMemoryUsage: String {
        return ByteCountFormatter.string(fromByteCount: Int64(memoryUsage), countStyle: .memory)
    }
    
    // 简化的健康状态评估
    var healthStatus: String {
        if memoryUsage > 300 * 1024 * 1024 { // 300MB
            return "内存使用过高"
        } else if networkErrors > 5 {
            return "网络不稳定"
        } else {
            return "运行正常"
        }
    }
}

/// 统一的浏览器管理器 - 整合所有WebView和URL处理功能
@MainActor
final class SimplifiedBrowserManager: ObservableObject {
    static let shared = SimplifiedBrowserManager()
    
    // MARK: - 核心状态
    @Published private(set) var activeWebViews: [UUID: WKWebView] = [:]
    @Published private(set) var memoryUsage: UInt64 = 0
    @Published private(set) var networkQuality: NetworkQuality = .fair
    @Published private(set) var currentMetrics = BrowserMetrics()
    
    // MARK: - 服务依赖
    private let logger = Logger(subsystem: "com.cop.browser", category: "SimplifiedBrowserManager")
    private let securityService = SecurityService.shared
    
    // MARK: - 配置管理（整合网络监控）
    private let baseConfiguration: WKWebViewConfiguration
    
    // 使用统一的网络监控器，避免多个服务冲突
    private let unifiedNetworkMonitor: BasicNetworkOptimizer
    
    // MARK: - 性能监控
    private var memoryCheckTimer: Timer?
    private var cleanupTimer: Timer?
    private var isMonitoring = false
    
    private init() {
        // 先创建基础配置
        let config = WKWebViewConfiguration()
        
        // 媒体配置 - 允许全屏播放
        config.allowsInlineMediaPlayback = true
        config.allowsAirPlayForMediaPlayback = true  // 允许AirPlay
        config.allowsPictureInPictureMediaPlayback = true  // 允许画中画
        config.mediaTypesRequiringUserActionForPlayback = []  // 允许自动播放以支持全屏
        
        // 安全配置
        config.preferences.javaScriptCanOpenWindowsAutomatically = false
        config.preferences.isFraudulentWebsiteWarningEnabled = true
        
        // 减少系统权限需求的配置
        config.processPool = WKProcessPool()
        config.websiteDataStore = WKWebsiteDataStore.default()
        config.selectionGranularity = .character
        config.dataDetectorTypes = [.phoneNumber, .link, .address]
        config.applicationNameForUserAgent = "cop Browser"
        
        // 网络优化配置
        config.preferences.minimumFontSize = 9.0
        config.upgradeKnownHostsToHTTPS = false  // 禁用自动HTTPS升级以减少网络错误
        
        // 设置更宽松的安全策略以避免权限问题
        if #available(iOS 14.0, *) {
            config.limitsNavigationsToAppBoundDomains = false
        }
        
        self.baseConfiguration = config
        self.unifiedNetworkMonitor = BasicNetworkOptimizer.shared
        setupMonitoring()
        logger.info("✅ SimplifiedBrowserManager 初始化完成 - 已优化权限配置")
    }
    
    deinit {
        Task { @MainActor [weak self] in
            self?.stopMonitoring()
        }
    }
    
    // MARK: - 性能监控接口
    func startMonitoring() {
        guard !isMonitoring else { return }
        isMonitoring = true
        setupMonitoring()
        logger.info("🔄 开始性能监控")
    }
    
    func stopMonitoring() {
        isMonitoring = false
        memoryCheckTimer?.invalidate()
        cleanupTimer?.invalidate()
        logger.info("⏹️ 停止性能监控")
    }
    
    // MARK: - 公共接口
    
    /// 创建WebView - 统一入口点
    func createWebView(for tab: NewBrowserTab, userAgent: String) -> WKWebView {
        // 复制基础配置，避免共享冲突
        let configuration = baseConfiguration.copy() as! WKWebViewConfiguration
        
        // 应用安全配置
        securityService.applySecurityConfiguration(to: configuration)
        
        // 创建WebView
        let webView = WKWebView(frame: .zero, configuration: configuration)
        
        // 配置WebView属性
        configureWebView(webView, userAgent: userAgent)
        
        // 注册管理
        activeWebViews[tab.id] = webView
        updateMetrics()
        
        logger.info("🚀 WebView创建: \(tab.id.uuidString.prefix(8))")
        return webView
    }
    
    /// 统一URL处理 - 整合所有URL相关逻辑
    func processURLInput(_ input: String) -> URL? {
        let trimmedInput = input.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedInput.isEmpty else { return nil }
        
        // 1. 完整URL检查
        if let url = URL(string: trimmedInput), url.scheme != nil {
            return securityService.enforceHTTPS(for: url)
        }
        
        // 2. 域名格式检查
        if isValidDomain(trimmedInput) {
            if let url = URL(string: "https://\(trimmedInput)") {
                return securityService.enforceHTTPS(for: url)
            }
        }
        
        // 3. 搜索查询
        let query = trimmedInput.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        return URL(string: "https://www.google.com/search?q=\(query)")
    }
    
    /// 统一URL安全验证
    func validateURLSecurity(_ url: URL) -> Bool {
        return !securityService.detectAdvancedThreats(url: url)
    }
    
    /// 加载URL - 统一处理
    func loadURL(_ url: URL, in webView: WKWebView, for tab: NewBrowserTab) {
        // 安全URL处理
        let secureURL = securityService.enforceHTTPS(for: url)
        
        // 创建优化请求
        let request = createNetworkRequest(for: secureURL)
        
        // 加载
        webView.load(request)
        
        logger.info("🌐 URL加载: \(secureURL.absoluteString.prefix(50))...")
    }
    
    /// 清理WebView - 统一处理
    func cleanupWebView(_ webView: WKWebView, for tabId: UUID) {
        webView.stopLoading()
        
        // 清理资源
        Task {
            await clearWebViewMemory(webView)
        }
        
        // 移除管理
        activeWebViews.removeValue(forKey: tabId)
        updateMetrics()
        
        logger.info("🧹 WebView清理: \(tabId.uuidString.prefix(8))")
    }
    
    /// 内存优化
    func optimizeMemory() async {
        let usage = getCurrentMemoryUsage()
        memoryUsage = usage
        currentMetrics.memoryUsage = usage
        
        // 简单的内存压力检测
        let threshold: UInt64 = 300 * 1024 * 1024 // 300MB for iPad mini
        if usage > threshold {
            await performMemoryCleanup()
        }
    }
    
    /// 用户体验优化 - 统一管理
    func optimizeUserExperience(_ webView: WKWebView) async {
        // 获取性能等级来调整优化策略
        let performanceGrade = BrowserOptimizer.shared.performanceGrade
        
        // 滚动体验优化
        let scrollView = webView.scrollView
        switch performanceGrade {
        case .excellent, .good:
            scrollView.decelerationRate = .normal
            scrollView.delaysContentTouches = false
        case .fair, .poor:
            scrollView.decelerationRate = .fast
            scrollView.delaysContentTouches = true
            scrollView.canCancelContentTouches = false
        }
        
        // 基础设置
        scrollView.showsVerticalScrollIndicator = true
        scrollView.showsHorizontalScrollIndicator = false
        scrollView.bounces = true
        scrollView.alwaysBounceVertical = false
        
        // iOS特定优化
        if #available(iOS 16.0, *) {
            scrollView.keyboardDismissMode = .onDrag
        }
        
        // 记录优化应用
        logger.info("用户体验优化已应用 - 性能等级: \(performanceGrade.rawValue)")
    }
    
    // MARK: - 网络请求统一处理（整合NetworkErrorHandler功能）
    /// 创建统一优化的网络请求
    func createNetworkRequest(for url: URL) -> URLRequest {
        // 使用NetworkErrorHandler的优化逻辑
        let errorHandler = NetworkErrorHandler.shared
        let optimizedRequest = errorHandler.createOptimizedURLRequest(for: url)
        
        // 进一步优化
        var request = optimizedRequest
        
        // 根据网络质量调整
        switch networkQuality {
        case .excellent:
            request.timeoutInterval = 15.0
            request.cachePolicy = .useProtocolCachePolicy
        case .good:
            request.timeoutInterval = 20.0
            request.cachePolicy = .returnCacheDataElseLoad
        case .fair:
            request.timeoutInterval = 30.0
            request.cachePolicy = .returnCacheDataElseLoad
        case .poor:
            request.timeoutInterval = 45.0
            request.cachePolicy = .returnCacheDataDontLoad
        }
        
        logger.info("🌐 创建优化网络请求: \(url.absoluteString.prefix(50))... [超时: \(request.timeoutInterval)s]")
        return request
    }
    
    /// 统一错误处理 - 委托给NetworkErrorHandler
    func handleNetworkError(_ error: Error, context: String = "", webView: WKWebView? = nil) {
        // 直接委托给NetworkErrorHandler处理所有错误逻辑
        NetworkErrorHandler.shared.handleNetworkError(error, context: context)
        
        // 仅处理浏览器特定的错误响应
        logger.error("🚨 浏览器网络错误: \(error.localizedDescription) - 上下文: \(context)")
        
        // 更新浏览器相关指标
        currentMetrics.networkErrors += 1
        
        // 如果有WebView，可以在这里进行浏览器特定的错误页面显示
        if let webView = webView {
            Task { @MainActor in
                await self.handleWebViewError(error, webView: webView)
            }
        }
    }
    
    /// 处理WebView特定的错误显示
    private func handleWebViewError(_ error: Error, webView: WKWebView) async {
        let nsError = error as NSError
        
        // 根据错误类型决定是否显示错误页面
        switch nsError.code {
        case NSURLErrorNotConnectedToInternet, NSURLErrorNetworkConnectionLost:
            // 网络错误，显示离线页面
            await showOfflineErrorPage(webView: webView)
        case NSURLErrorTimedOut:
            // 超时错误，显示重试页面
            await showTimeoutErrorPage(webView: webView)
        default:
            // 其他错误，使用默认处理
            break
        }
    }
    
    /// 显示离线错误页面
    private func showOfflineErrorPage(webView: WKWebView) async {
        let errorHTML = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>网络连接错误</title>
            <style>
                body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; text-align: center; padding: 40px; }
                .error-icon { font-size: 64px; margin-bottom: 20px; }
                .error-title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
                .error-message { font-size: 16px; color: #666; margin-bottom: 30px; }
                .retry-button { background: #007AFF; color: white; border: none; padding: 12px 24px; border-radius: 8px; font-size: 16px; cursor: pointer; }
            </style>
        </head>
        <body>
            <div class="error-icon">📡</div>
            <div class="error-title">网络连接失败</div>
            <div class="error-message">请检查您的网络连接后重试</div>
            <button class="retry-button" onclick="window.location.reload()">重新加载</button>
        </body>
        </html>
        """
        
        webView.loadHTMLString(errorHTML, baseURL: nil)
    }
    
    /// 显示超时错误页面
    private func showTimeoutErrorPage(webView: WKWebView) async {
        let errorHTML = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>连接超时</title>
            <style>
                body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; text-align: center; padding: 40px; }
                .error-icon { font-size: 64px; margin-bottom: 20px; }
                .error-title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
                .error-message { font-size: 16px; color: #666; margin-bottom: 30px; }
                .retry-button { background: #007AFF; color: white; border: none; padding: 12px 24px; border-radius: 8px; font-size: 16px; cursor: pointer; }
            </style>
        </head>
        <body>
            <div class="error-icon">⏱️</div>
            <div class="error-title">连接超时</div>
            <div class="error-message">服务器响应时间过长，请稍后重试</div>
            <button class="retry-button" onclick="window.location.reload()">重新加载</button>
        </body>
        </html>
        """
        
        webView.loadHTMLString(errorHTML, baseURL: nil)
    }
    
    /// 获取网络诊断信息
    func getNetworkDiagnostics() -> NetworkDiagnostics {
        return NetworkErrorHandler.shared.getNetworkDiagnostics()
    }
    
    // MARK: - 私有方法
    
    private func updateMetrics() {
        currentMetrics.activeWebViews = activeWebViews.count
        currentMetrics.memoryUsage = getCurrentMemoryUsage()
    }
    
    private func configureWebView(_ webView: WKWebView, userAgent: String) {
        // 用户代理
        webView.customUserAgent = userAgent
        
        // 基础属性
        webView.isOpaque = false
        webView.backgroundColor = UIColor.clear
        webView.allowsLinkPreview = true
        webView.allowsBackForwardNavigationGestures = true
        
        // 滚动优化
        let scrollView = webView.scrollView
        scrollView.showsVerticalScrollIndicator = true
        scrollView.showsHorizontalScrollIndicator = false
        scrollView.delaysContentTouches = false
        scrollView.canCancelContentTouches = true
        
        // iOS 16+ 优化
        if #available(iOS 16.0, *) {
            scrollView.keyboardDismissMode = .onDrag
        }
        
        // 应用安全服务的Cookie策略（在WebView创建后）
        securityService.applyCookiePolicy(to: webView)
    }
    
    private func setupMonitoring() {
        guard isMonitoring else { return }
        
        // 内存检查 - 每30秒
        memoryCheckTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.optimizeMemory()
            }
        }
        
        // 定期清理 - 每5分钟
        cleanupTimer = Timer.scheduledTimer(withTimeInterval: 300.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.performRoutineCleanup()
            }
        }
        
        // 网络状态监听
        unifiedNetworkMonitor.$currentNetworkQuality
            .assign(to: &$networkQuality)
    }
    
    // MARK: - 域名验证（统一方法）
    private func isValidDomain(_ input: String) -> Bool {
        let domainRegex = "^([a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.)+(com|net|org|edu|gov|mil|biz|info|mobi|name|aero|jobs|museum|[a-z]{2})$"
        let domainPredicate = NSPredicate(format: "SELF MATCHES %@", domainRegex)
        return domainPredicate.evaluate(with: input.lowercased())
    }
    
    // MARK: - 内存管理方法
    private func getCurrentMemoryUsage() -> UInt64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        return (kerr == KERN_SUCCESS) ? info.resident_size : 0
    }
    
    private func clearWebViewMemory(_ webView: WKWebView) async {
        // 清理WebView缓存
        let dataStore = webView.configuration.websiteDataStore
        await dataStore.removeData(ofTypes: [WKWebsiteDataTypeMemoryCache], modifiedSince: Date.distantPast)
    }
    
    private func performMemoryCleanup() async {
        logger.info("🧹 执行内存清理")
        
        // 清理非活跃WebView
        for (tabId, webView) in activeWebViews {
            if webView.superview == nil {
                await clearWebViewMemory(webView)
                logger.info("🧹 清理非活跃WebView: \(tabId.uuidString.prefix(8))")
            }
        }
    }
    
    private func performRoutineCleanup() async {
        logger.info("🔄 执行例行清理")
        await optimizeMemory()
    }
} 